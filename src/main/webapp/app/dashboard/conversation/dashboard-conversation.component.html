<div class="conversation-panel">
  <!-- Header -->
  <div class="conversation-header">
    <div class="header-title">
      <fa-icon [icon]="faComments" class="header-icon"></fa-icon>
      <h3>Conversaciones</h3>
    </div>
    <button type="button" class="btn btn-primary btn-sm" (click)="openCreateModal()" [disabled]="isLoading()">
      <fa-icon [icon]="faPlus"></fa-icon>
    </button>
  </div>

  <!-- Conversation List -->
  <div class="conversation-list">
    @if (isLoading()) {
      <div class="loading-state">
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <span class="ms-2">Cargando conversaciones...</span>
      </div>
    } @else if (conversations().length === 0) {
      <div class="empty-state">
        <p>No tienes conversaciones registradas.</p>
        <small>Haz clic en el botón + para crear tu primera conversación.</small>
      </div>
    } @else {
      @for (conversation of conversations(); track conversation.id) {
        <div class="conversation-item" [class.expanded]="isConversationExpanded(conversation.id!)">
          <div class="conversation-header-item" (click)="toggleConversation(conversation.id!)">
            <div class="conversation-info">
              <div class="conversation-name">{{ conversation.name }}</div>
              <div class="conversation-date">
                <fa-icon [icon]="faCalendar" class="date-icon"></fa-icon>
                {{ formatDate(conversation.start) }}
              </div>
              <div class="conversation-summary">{{ getConversationSummary(conversation) }}</div>
            </div>
            <div class="conversation-meta">
              @if (conversation.feelingList && conversation.feelingList.length > 0) {
                <div class="feelings-preview">
                  @for (feeling of conversation.feelingList.slice(0, 3); track feeling) {
                    <span class="feeling-emoji">{{ getFeelingEmoji(feeling) }}</span>
                  }
                  @if (conversation.feelingList.length > 3) {
                    <span class="more-indicator">+{{ conversation.feelingList.length - 3 }}</span>
                  }
                </div>
              }
              <button
                type="button"
                class="btn btn-outline-primary btn-sm"
                (click)="openEditModal(conversation); $event.stopPropagation()"
                [disabled]="isLoading()"
                title="Editar conversación"
              >
                <fa-icon [icon]="faEdit"></fa-icon>
              </button>
            </div>
          </div>

          @if (isConversationExpanded(conversation.id!)) {
            <div class="conversation-details">
              @if (conversation.context) {
                <div class="context-section">
                  <strong>Contexto:</strong>
                  <p>{{ conversation.context }}</p>
                </div>
              }

              @if (conversation.tagList && conversation.tagList.length > 0) {
                <div class="tags-section">
                  <strong>Tags:</strong>
                  <div class="tag-list">
                    @for (tag of conversation.tagList; track tag.id) {
                      <span class="tag-badge">
                        <fa-icon [icon]="faTag"></fa-icon>
                        {{ tag.value }}
                      </span>
                    }
                  </div>
                </div>
              }

              @if (conversation.feelingList && conversation.feelingList.length > 0) {
                <div class="feelings-section">
                  <strong>Sentimientos:</strong>
                  <div class="feeling-list">
                    @for (feeling of conversation.feelingList; track feeling) {
                      <span class="feeling-badge"> {{ getFeelingEmoji(feeling) }} {{ getFeelingLabel(feeling) }} </span>
                    }
                  </div>
                </div>
              }
            </div>
          }
        </div>
      }
    }
  </div>
</div>

<!-- Create/Edit Modal -->
@if (showModal() && currentConversation) {
  <jhi-dashboard-modal-conversation
    [conversation]="currentConversation"
    [isEditMode]="isEditMode()"
    [availableTags]="availableTags()"
    [allMessages]="allMessages"
    (save)="onSaveConversation($event)"
    (delete)="onDeleteConversation()"
    (close)="closeModal()"
  ></jhi-dashboard-modal-conversation>
}
