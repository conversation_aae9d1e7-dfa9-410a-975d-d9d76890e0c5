import { CommonModule } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, inject, Input, OnInit, Output, signal, computed, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faTimes,
  faSave,
  faTrash,
  faCalendar,
  faTag,
  faSmile,
  faPlus,
  faMinus,
  faExclamationTriangle,
} from '@fortawesome/free-solid-svg-icons';
import { ConversationDTO, Feeling, TagDTO } from './../dashboard-conversation-model';
import { DashboardFilterComponent } from '../../filter/dashboard-filter.component';
import { DashboardMessageComponent } from '../../messsage/dashboard-message.component';
import { IMessage, FEELING_INFO } from '../../messsage/dashboard-message-model';
import { DashboardFilterModel } from '../../filter/dashboard-filter-model';
import dayjs from 'dayjs/esm';
import { catchError, of } from 'rxjs';

@Component({
  selector: 'jhi-dashboard-modal-conversation',
  templateUrl: './dashboard-modal-conversation.component.html',
  styleUrls: ['./dashboard-modal-conversation.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule, DashboardFilterComponent, DashboardMessageComponent],
})
export class DashboardModalConversationComponent implements OnInit {
  // Icons
  readonly faTimes = faTimes;
  readonly faSave = faSave;
  readonly faTrash = faTrash;
  readonly faCalendar = faCalendar;
  readonly faTag = faTag;
  readonly faSmile = faSmile;
  readonly faPlus = faPlus;
  readonly faMinus = faMinus;
  readonly faExclamationTriangle = faExclamationTriangle;

  // Inputs/Outputs
  @Input() conversation: ConversationDTO | null = null;
  @Input() isEditMode = false;
  @Input() availableTags: TagDTO[] = [];
  @Input() allMessages: IMessage[] = [];
  @Output() save = new EventEmitter<ConversationDTO>();
  @Output() delete = new EventEmitter<void>();
  @Output() close = new EventEmitter<void>();

  // Services
  private readonly http = inject(HttpClient);
  private readonly destroyRef = inject(DestroyRef);

  // State signals
  readonly isLoading = signal(false);
  readonly allMessagesSignal = signal<IMessage[]>([]);
  readonly filteredMessages = signal<IMessage[]>([]);
  readonly conversationMessages = signal<IMessage[]>([]);
  readonly showFeelingModal = signal(false);
  readonly showTagModal = signal(false);
  readonly errorMessage = signal<string | null>(null);

  // Computed values
  readonly hasUnsavedChanges = computed(() => {
    if (!this.conversation) return this.conversationMessages().length > 0;

    const originalMessageIds = new Set(this.conversation.messageList?.map(m => m.id) || []);
    const currentMessageIds = new Set(this.conversationMessages().map(m => m.id));

    if (originalMessageIds.size !== currentMessageIds.size) return true;

    for (const id of currentMessageIds) {
      if (!originalMessageIds.has(id)) return true;
    }

    return false;
  });

  // Form data
  currentConversation: ConversationDTO = {
    name: '',
    context: '',
    feelingList: [],
    tagList: [],
    messageList: [],
  };

  // Date inputs
  startDate = '';
  startTime = '';
  endDate = '';
  endTime = '';

  // Available feelings
  readonly feelings = Object.values(Feeling);
  readonly feelingInfo = FEELING_INFO;

  ngOnInit(): void {
    this.initializeConversation();
    this.loadAllMessages();
  }

  private initializeConversation(): void {
    if (this.conversation) {
      // Deep clone the conversation to avoid mutating the original
      this.currentConversation = {
        ...this.conversation,
        feelingList: [...(this.conversation.feelingList || [])],
        tagList: [...(this.conversation.tagList || [])],
        messageList: [...(this.conversation.messageList || [])],
      };

      // Initialize date/time fields
      if (this.conversation.start) {
        const start = dayjs(this.conversation.start);
        this.startDate = start.format('YYYY-MM-DD');
        this.startTime = start.format('HH:mm');
      }
      if (this.conversation.end) {
        const end = dayjs(this.conversation.end);
        this.endDate = end.format('YYYY-MM-DD');
        this.endTime = end.format('HH:mm');
      }

      // Load conversation messages - convert MessageDTO to IMessage
      if (this.currentConversation.messageList && this.currentConversation.messageList.length > 0) {
        const messages: IMessage[] = this.currentConversation.messageList.map(msg => ({
          id: msg.id,
          content: msg.content || null,
          time: msg.time,
          sender: msg.sender || null,
          recipient: msg.recipient || null,
          feeling: msg.feeling,
          tag: msg.tag || undefined,
          type: null,
          feelingList: [],
        }));
        this.conversationMessages.set(messages);
      } else {
        this.conversationMessages.set([]);
      }
    } else {
      // Initialize empty conversation
      this.currentConversation = {
        name: '',
        context: '',
        feelingList: [],
        tagList: [],
        messageList: [],
      };
    }
  }

  private loadAllMessages(): void {
    if (this.allMessages && this.allMessages.length > 0) {
      // Use provided messages
      this.allMessagesSignal.set(this.allMessages);
      this.filteredMessages.set(this.allMessages);
    } else {
      // Load from API
      this.isLoading.set(true);
      this.errorMessage.set(null);

      this.http
        .get<IMessage[]>('/api/messages')
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          catchError((error: HttpErrorResponse) => {
            console.error('Error loading messages:', error);
            this.errorMessage.set('Error al cargar los mensajes. Por favor, intenta de nuevo.');
            return of([]);
          }),
        )
        .subscribe(messages => {
          this.allMessagesSignal.set(messages);
          this.filteredMessages.set(messages);
          this.isLoading.set(false);
        });
    }
  }

  onFilterChange(filter: DashboardFilterModel): void {
    const allMessages = this.allMessagesSignal();

    const filtered = allMessages.filter(message => {
      // Text filter
      if (filter.content && !message.content?.toLowerCase().includes(filter.content.toLowerCase())) {
        return false;
      }

      // Date range filter
      if (filter.init && message.time) {
        const messageDate = dayjs(message.time);
        if (messageDate.isBefore(filter.init)) {
          return false;
        }
      }

      if (filter.end && message.time) {
        const messageDate = dayjs(message.time);
        if (messageDate.isAfter(filter.end)) {
          return false;
        }
      }

      // Tag filter
      if (filter.tag && filter.tag.length > 0) {
        const filterTagValue = filter.tag[0].value;
        if (message.tag !== filterTagValue) {
          return false;
        }
      }

      // Feeling filter
      if (filter.feelings && filter.feelings.length > 0) {
        const filterFeeling = filter.feelings[0];
        if (message.feeling !== filterFeeling) {
          return false;
        }
      }

      return true;
    });

    this.filteredMessages.set(filtered);
  }

  onClearFilters(): void {
    this.filteredMessages.set(this.allMessagesSignal());
  }

  isMessageInConversation(message: IMessage): boolean {
    return this.conversationMessages().some(m => m.id === message.id);
  }

  toggleMessageInConversation(message: IMessage): void {
    if (!message.id) {
      console.warn('Cannot toggle message without ID');
      return;
    }

    const currentMessages = this.conversationMessages();
    const isInConversation = this.isMessageInConversation(message);

    if (isInConversation) {
      // Remove from conversation
      this.conversationMessages.set(currentMessages.filter(m => m.id !== message.id));
    } else {
      // Add to conversation (maintain chronological order)
      const newMessages = [...currentMessages, message].sort((a, b) => {
        if (!a.time || !b.time) return 0;
        return dayjs(a.time).valueOf() - dayjs(b.time).valueOf();
      });
      this.conversationMessages.set(newMessages);
    }

    // Update the conversation object - convert IMessage to MessageDTO
    this.currentConversation.messageList = this.conversationMessages().map(msg => ({
      id: msg.id,
      content: msg.content || undefined,
      time: msg.time || undefined,
      sender: msg.sender || undefined,
      recipient: msg.recipient || undefined,
      feeling: msg.feeling,
      tag: msg.tag,
    }));
  }

  updateDateTime(): void {
    try {
      if (this.startDate && this.startTime) {
        const startDateTime = dayjs(`${this.startDate} ${this.startTime}`);
        if (startDateTime.isValid()) {
          this.currentConversation.start = startDateTime;
        } else {
          console.warn('Invalid start date/time');
        }
      } else {
        this.currentConversation.start = undefined;
      }

      if (this.endDate && this.endTime) {
        const endDateTime = dayjs(`${this.endDate} ${this.endTime}`);
        if (endDateTime.isValid()) {
          this.currentConversation.end = endDateTime;
        } else {
          console.warn('Invalid end date/time');
        }
      } else {
        this.currentConversation.end = undefined;
      }

      // Validate that end is after start
      if (this.currentConversation.start && this.currentConversation.end) {
        if (this.currentConversation.end.isBefore(this.currentConversation.start)) {
          this.errorMessage.set('La fecha de fin debe ser posterior a la fecha de inicio');
        } else {
          this.errorMessage.set(null);
        }
      }
    } catch (error) {
      console.error('Error updating date/time:', error);
      this.errorMessage.set('Error al actualizar las fechas');
    }
  }

  toggleFeeling(feeling: Feeling): void {
    if (!this.currentConversation.feelingList) {
      this.currentConversation.feelingList = [];
    }

    const index = this.currentConversation.feelingList.indexOf(feeling);
    if (index > -1) {
      this.currentConversation.feelingList = this.currentConversation.feelingList.filter(f => f !== feeling);
    } else {
      this.currentConversation.feelingList = [...this.currentConversation.feelingList, feeling];
    }
  }

  isFeelingSelected(feeling: Feeling): boolean {
    return this.currentConversation.feelingList?.includes(feeling) || false;
  }

  getFeelingEmoji(feeling: Feeling): string {
    return this.feelingInfo[feeling]?.emoji || '❓';
  }

  getFeelingLabel(feeling: Feeling): string {
    return this.feelingInfo[feeling]?.label || feeling;
  }

  addTagToConversation(tag: TagDTO): void {
    if (!this.currentConversation.tagList) {
      this.currentConversation.tagList = [];
    }

    const isAlreadyAdded = this.currentConversation.tagList.some(t => t.id === tag.id);
    if (!isAlreadyAdded) {
      this.currentConversation.tagList = [...this.currentConversation.tagList, tag];
    }
  }

  removeTagFromConversation(tag: TagDTO): void {
    if (this.currentConversation.tagList) {
      this.currentConversation.tagList = this.currentConversation.tagList.filter(t => t.id !== tag.id);
    }
  }

  isTagSelected(tag: TagDTO): boolean {
    return this.currentConversation.tagList?.some(t => t.id === tag.id) || false;
  }

  saveConversation(): void {
    // Validate before saving
    if (!this.validateConversation()) {
      return;
    }

    this.updateDateTime();

    // Ensure we have clean data - convert IMessage to MessageDTO
    const conversationToSave: ConversationDTO = {
      ...this.currentConversation,
      messageList: this.conversationMessages().map(msg => ({
        id: msg.id,
        content: msg.content || undefined,
        time: msg.time || undefined,
        sender: msg.sender || undefined,
        recipient: msg.recipient || undefined,
        feeling: msg.feeling,
        tag: msg.tag,
      })),
    };

    this.save.emit(conversationToSave);
  }

  private validateConversation(): boolean {
    this.errorMessage.set(null);

    // Check if name is provided
    if (!this.currentConversation.name || this.currentConversation.name.trim() === '') {
      this.errorMessage.set('Debes proporcionar un nombre para la conversación');
      return false;
    }

    // Check if there are messages
    if (this.conversationMessages().length === 0) {
      this.errorMessage.set('Debes agregar al menos un mensaje a la conversación');
      return false;
    }

    // Check date validity
    if (this.currentConversation.start && this.currentConversation.end) {
      if (this.currentConversation.end.isBefore(this.currentConversation.start)) {
        this.errorMessage.set('La fecha de fin debe ser posterior a la fecha de inicio');
        return false;
      }
    }

    return true;
  }

  deleteConversation(): void {
    if (confirm('¿Estás seguro de que quieres eliminar esta conversación? Esta acción no se puede deshacer.')) {
      this.delete.emit();
    }
  }

  closeModal(): void {
    if (this.hasUnsavedChanges()) {
      if (confirm('Tienes cambios sin guardar. ¿Estás seguro de que quieres cerrar?')) {
        this.close.emit();
      }
    } else {
      this.close.emit();
    }
  }

  trackMessageById(index: number, item: IMessage): string | number {
    return item.id ?? index;
  }

  trackTagById(index: number, item: TagDTO): string | number {
    return item.id ?? index;
  }

  trackFeelingByValue(index: number, item: Feeling): string {
    return item;
  }
}
