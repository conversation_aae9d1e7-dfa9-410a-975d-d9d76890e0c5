.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 90vh;
  display: flex;
  flex-direction: column;

  &.modal-lg {
    width: 90%;
    max-width: 1400px;
  }
}

.modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
  }

  .btn-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;

    &:hover {
      color: #495057;
    }
  }
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
  max-height: calc(90vh - 120px);
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

// Messages Section
.messages-section {
  border-right: 1px solid #dee2e6;
  padding-right: 1.5rem;
}

.messages-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: #495057;
}

.messages-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.5rem;
}

.message-item-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;

  jhi-dashboard-message {
    flex: 1;
  }
}

.message-action-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  flex-shrink: 0;

  &.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;

    &:hover {
      background-color: #218838;
      border-color: #1e7e34;
    }
  }

  &.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;

    &:hover {
      background-color: #c82333;
      border-color: #bd2130;
    }
  }
}

.selected-messages {
  margin-top: 2rem;

  .messages-list {
    background-color: #f8f9fa;
  }
}

// Details Section
.details-section {
  padding-left: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;

  .form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #495057;
  }
}

.feelings-display,
.tags-display {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.75rem;
  min-height: 60px;
}

.selected-feelings,
.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.feeling-badge,
.tag-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: #e9ecef;
  border-radius: 4px;
  font-size: 0.875rem;
}

.empty-state-small {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  color: #6c757d;

  fa-icon {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
  }
}

// Sub-modals for feelings and tags
.sub-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1060;
}

.sub-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
}

.sub-modal-header {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h5 {
    margin: 0;
    font-size: 1.1rem;
  }
}

.sub-modal-body {
  padding: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.sub-modal-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
}

.feelings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.5rem;
}

.feeling-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  background-color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }

  &.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
  }

  .feeling-emoji {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
  }

  .feeling-label {
    font-size: 0.875rem;
    color: #495057;
  }
}

.available-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-select-button {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #dee2e6;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }

  &.selected {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;
  text-align: center;
}

// Alert styles
.alert {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;

  .btn-close {
    padding: 0.5rem;
    margin-left: auto;
  }
}

// Improved scrollbar styles
.messages-list {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;

    &:hover {
      background: #555;
    }
  }
}

// Animation for messages
.message-item-wrapper {
  transition: background-color 0.2s ease;
  padding: 0.25rem;
  border-radius: 4px;

  &:hover {
    background-color: rgba(0, 123, 255, 0.05);
  }
}

// Improved button states
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

// Responsive adjustments
@media (max-width: 768px) {
  .modal-content.modal-lg {
    width: 95%;
  }

  .messages-section {
    border-right: none;
    border-bottom: 1px solid #dee2e6;
    padding-right: 0;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .details-section {
    padding-left: 0;
  }
}
