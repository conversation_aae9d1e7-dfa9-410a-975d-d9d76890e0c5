<div class="modal-overlay" (click)="closeModal()">
  <div class="modal-content modal-lg" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h4>{{ isEditMode ? 'Editar Conversación' : 'Crear Conversación' }}</h4>
      <button type="button" class="btn-close" (click)="closeModal()">
        <fa-icon [icon]="faTimes"></fa-icon>
      </button>
    </div>

    <div class="modal-body">
      <!-- Error Message Display -->
      @if (errorMessage()) {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <fa-icon [icon]="faExclamationTriangle" class="me-2"></fa-icon>
          {{ errorMessage() }}
          <button type="button" class="btn-close" (click)="errorMessage.set(null)" aria-label="Close"></button>
        </div>
      }
      <div class="row">
        <!-- Left side: Messages (2/3) -->
        <div class="col-md-8 messages-section">
          <!-- Filter Component -->
          <div class="filter-wrapper mb-3">
            <jhi-dashboard-filter
              [tags]="[]"
              (filterChange)="onFilterChange($event)"
              (clearFilters)="onClearFilters()"
            ></jhi-dashboard-filter>
          </div>

          <!-- Messages List -->
          <div class="messages-container">
            <h5 class="section-title">Mensajes Disponibles</h5>

            @if (isLoading()) {
              <div class="loading-state">
                <div class="spinner-border spinner-border-sm" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <span class="ms-2">Cargando mensajes...</span>
              </div>
            } @else if (allMessagesSignal().length === 0) {
              <div class="empty-state">
                <p class="text-muted">No hay mensajes disponibles</p>
              </div>
            } @else {
              <div class="messages-list">
                @for (message of filteredMessages(); track trackMessageById($index, message)) {
                  @if (!isMessageInConversation(message)) {
                    <div class="message-item-wrapper">
                      <jhi-dashboard-message [message]="message"></jhi-dashboard-message>
                      <button
                        type="button"
                        class="btn btn-sm btn-success message-action-btn"
                        (click)="toggleMessageInConversation(message)"
                        title="Agregar a la conversación"
                      >
                        <fa-icon [icon]="faPlus"></fa-icon>
                      </button>
                    </div>
                  }
                }
              </div>
            }

            <!-- Selected Messages -->
            @if (conversationMessages().length > 0) {
              <div class="selected-messages mt-4">
                <h5 class="section-title">Mensajes en la Conversación ({{ conversationMessages().length }})</h5>
                <div class="messages-list">
                  @for (message of conversationMessages(); track trackMessageById($index, message)) {
                    <div class="message-item-wrapper">
                      <jhi-dashboard-message
                        [message]="message"
                        [actionType]="'remove'"
                        (actionClicked)="toggleMessageInConversation($event)"
                      ></jhi-dashboard-message>
                    </div>
                  }
                </div>
              </div>
            }
          </div>
        </div>

        <!-- Right side: Conversation details (1/3) -->
        <div class="col-md-4 details-section">
          <h5 class="section-title">Detalles de la Conversación</h5>

          <!-- Name Field -->
          <div class="form-group mb-3">
            <label for="conversationName" class="form-label">Nombre de la Conversación:</label>
            <input
              id="conversationName"
              type="text"
              class="form-control form-control-sm"
              placeholder="Ingresa un nombre para la conversación"
              [(ngModel)]="currentConversation.name"
              required
            />
          </div>

          <!-- Date/Time Fields -->
          <div class="form-group mb-3">
            <label class="form-label">
              <fa-icon [icon]="faCalendar"></fa-icon>
              Fecha y Hora de Inicio
            </label>
            <div class="row g-2">
              <div class="col-7">
                <input type="date" class="form-control form-control-sm" [(ngModel)]="startDate" (change)="updateDateTime()" />
              </div>
              <div class="col-5">
                <input type="time" class="form-control form-control-sm" [(ngModel)]="startTime" (change)="updateDateTime()" />
              </div>
            </div>
          </div>

          <div class="form-group mb-3">
            <label class="form-label">
              <fa-icon [icon]="faCalendar"></fa-icon>
              Fecha y Hora de Fin
            </label>
            <div class="row g-2">
              <div class="col-7">
                <input type="date" class="form-control form-control-sm" [(ngModel)]="endDate" (change)="updateDateTime()" />
              </div>
              <div class="col-5">
                <input type="time" class="form-control form-control-sm" [(ngModel)]="endTime" (change)="updateDateTime()" />
              </div>
            </div>
          </div>

          <!-- Feelings Section -->
          <div class="form-group mb-3">
            <label class="form-label">
              <fa-icon [icon]="faSmile"></fa-icon>
              Sentimientos
            </label>
            <div class="feelings-display">
              @if (currentConversation.feelingList && currentConversation.feelingList.length > 0) {
                <div class="selected-feelings">
                  @for (feeling of currentConversation.feelingList; track feeling) {
                    <span class="feeling-badge"> {{ getFeelingEmoji(feeling) }} {{ getFeelingLabel(feeling) }} </span>
                  }
                </div>
              } @else {
                <div class="empty-state-small">
                  <fa-icon [icon]="faSmile" class="text-muted"></fa-icon>
                  <small class="text-muted">Sin sentimientos</small>
                </div>
              }
              <button type="button" class="btn btn-outline-primary btn-sm mt-2 w-100" (click)="showFeelingModal.set(true)">
                <fa-icon [icon]="faPlus"></fa-icon>
                Gestionar Sentimientos
              </button>
            </div>
          </div>

          <!-- Tags Section -->
          <div class="form-group mb-3">
            <label class="form-label">
              <fa-icon [icon]="faTag"></fa-icon>
              Etiquetas
            </label>
            <div class="tags-display">
              @if (currentConversation.tagList && currentConversation.tagList.length > 0) {
                <div class="selected-tags">
                  @for (tag of currentConversation.tagList; track trackTagById($index, tag)) {
                    <span class="tag-badge">
                      <fa-icon [icon]="faTag"></fa-icon>
                      {{ tag.value }}
                    </span>
                  }
                </div>
              } @else {
                <div class="empty-state-small">
                  <fa-icon [icon]="faTag" class="text-muted"></fa-icon>
                  <small class="text-muted">Sin etiquetas</small>
                </div>
              }
              <button type="button" class="btn btn-outline-primary btn-sm mt-2 w-100" (click)="showTagModal.set(true)">
                <fa-icon [icon]="faPlus"></fa-icon>
                Gestionar Etiquetas
              </button>
            </div>
          </div>

          <!-- Context -->
          <div class="form-group">
            <label for="conversationContext" class="form-label">Contexto:</label>
            <textarea
              id="conversationContext"
              class="form-control form-control-sm"
              rows="4"
              placeholder="Describe el contexto de la conversación..."
              [(ngModel)]="currentConversation.context"
            ></textarea>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-success" (click)="saveConversation()" [disabled]="isLoading()">
        <fa-icon [icon]="faSave"></fa-icon>
        {{ isEditMode ? 'Guardar' : 'Crear' }}
      </button>
      @if (isEditMode) {
        <button type="button" class="btn btn-danger" (click)="deleteConversation()" [disabled]="isLoading()">
          <fa-icon [icon]="faTrash"></fa-icon>
          Eliminar
        </button>
      }
      <button type="button" class="btn btn-secondary" (click)="closeModal()">Cancelar</button>
    </div>
  </div>
</div>

<!-- Feeling Selection Modal -->
@if (showFeelingModal()) {
  <div class="sub-modal-overlay" (click)="showFeelingModal.set(false)">
    <div class="sub-modal-content" (click)="$event.stopPropagation()">
      <div class="sub-modal-header">
        <h5>Seleccionar Sentimientos</h5>
        <button type="button" class="btn-close" (click)="showFeelingModal.set(false)">
          <fa-icon [icon]="faTimes"></fa-icon>
        </button>
      </div>
      <div class="sub-modal-body">
        <div class="feelings-grid">
          @for (feeling of feelings; track trackFeelingByValue($index, feeling)) {
            <button type="button" class="feeling-button" [class.selected]="isFeelingSelected(feeling)" (click)="toggleFeeling(feeling)">
              <span class="feeling-emoji">{{ getFeelingEmoji(feeling) }}</span>
              <span class="feeling-label">{{ getFeelingLabel(feeling) }}</span>
            </button>
          }
        </div>
      </div>
      <div class="sub-modal-footer">
        <button type="button" class="btn btn-primary btn-sm" (click)="showFeelingModal.set(false)">Aceptar</button>
      </div>
    </div>
  </div>
}

<!-- Tag Selection Modal -->
@if (showTagModal()) {
  <div class="sub-modal-overlay" (click)="showTagModal.set(false)">
    <div class="sub-modal-content" (click)="$event.stopPropagation()">
      <div class="sub-modal-header">
        <h5>Seleccionar Etiquetas</h5>
        <button type="button" class="btn-close" (click)="showTagModal.set(false)">
          <fa-icon [icon]="faTimes"></fa-icon>
        </button>
      </div>
      <div class="sub-modal-body">
        <div class="available-tags">
          @for (tag of availableTags; track trackTagById($index, tag)) {
            <button
              type="button"
              class="tag-select-button"
              [class.selected]="isTagSelected(tag)"
              (click)="isTagSelected(tag) ? removeTagFromConversation(tag) : addTagToConversation(tag)"
            >
              <fa-icon [icon]="faTag"></fa-icon>
              {{ tag.value }}
            </button>
          }
        </div>
      </div>
      <div class="sub-modal-footer">
        <button type="button" class="btn btn-primary btn-sm" (click)="showTagModal.set(false)">Aceptar</button>
      </div>
    </div>
  </div>
}
