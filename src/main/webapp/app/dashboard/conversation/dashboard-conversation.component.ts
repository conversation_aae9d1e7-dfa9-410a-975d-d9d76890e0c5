import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, inject, Input, OnInit, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faComments, faPlus, faEdit, faTrash, faTimes, faSave, faCalendar, faTag, faSmile } from '@fortawesome/free-solid-svg-icons';
import { ConversationDTO, TagDTO } from './dashboard-conversation-model';
import { Feeling, FEELING_INFO } from 'app/shared/enum/feeling';
import { DashboardModalConversationComponent } from './modal/dashboard-modal-conversation.component';
import { IMessage } from '../messsage/dashboard-message-model';
import dayjs, { Dayjs } from 'dayjs/esm';

@Component({
  selector: 'jhi-dashboard-conversation',
  templateUrl: './dashboard-conversation.component.html',
  styleUrls: ['./dashboard-conversation.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule, DashboardModalConversationComponent],
})
export class DashboardConversationComponent implements OnInit {
  @Input() allMessages: IMessage[] = [];

  faComments = faComments;
  faPlus = faPlus;
  faEdit = faEdit;
  faTrash = faTrash;
  faTimes = faTimes;
  faSave = faSave;
  faCalendar = faCalendar;
  faTag = faTag;
  faSmile = faSmile;

  private readonly http = inject(HttpClient);

  conversations = signal<ConversationDTO[]>([]);
  availableTags = signal<TagDTO[]>([]);
  isLoading = signal(false);
  showModal = signal(false);
  isEditMode = signal(false);
  expandedConversations = signal<Set<string>>(new Set());

  // Form data
  currentConversation: ConversationDTO | null = null;

  ngOnInit(): void {
    this.loadConversations();
    this.loadAvailableTags();
  }

  loadConversations(): void {
    this.isLoading.set(true);
    this.http.get<ConversationDTO[]>('/api/conversations/my').subscribe({
      next: conversations => {
        const processedConversations = conversations.map(conv => ({
          ...conv,
          messageList:
            conv.messageList?.map(msg => ({
              ...msg,
              time: msg.time ? dayjs(msg.time) : undefined,
            })) || [],
        }));
        this.conversations.set(processedConversations);
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Error loading conversations:', error);
        this.isLoading.set(false);
      },
    });
  }

  loadAvailableTags(): void {
    this.http.get<TagDTO[]>('/api/tags/my').subscribe({
      next: tags => {
        this.availableTags.set(tags);
      },
      error: error => {
        console.error('Error loading tags:', error);
      },
    });
  }

  toggleConversation(conversationId: string): void {
    const expanded = new Set(this.expandedConversations());
    if (expanded.has(conversationId)) {
      expanded.delete(conversationId);
    } else {
      expanded.add(conversationId);
    }
    this.expandedConversations.set(expanded);
  }

  isConversationExpanded(conversationId: string): boolean {
    return this.expandedConversations().has(conversationId);
  }

  openCreateModal(): void {
    this.isEditMode.set(false);
    this.currentConversation = {
      context: '',
      feelingList: [],
      tagList: [],
      messageList: [],
    };
    this.showModal.set(true);
  }

  openEditModal(conversation: ConversationDTO): void {
    this.isEditMode.set(true);
    // Deep clone and convert message times to Dayjs objects
    this.currentConversation = {
      ...conversation,
      messageList:
        conversation.messageList?.map(msg => ({
          ...msg,
          time: msg.time ? dayjs(msg.time) : undefined,
        })) || [],
      feelingList: conversation.feelingList ? [...conversation.feelingList] : [],
      tagList: conversation.tagList ? [...conversation.tagList] : [],
    };
    this.showModal.set(true);
  }

  onSaveConversation(conversation: ConversationDTO): void {
    this.isLoading.set(true);
    console.log('Conversation to save:', conversation);

    if (this.isEditMode()) {
      // Update existing conversation
      this.http.put<ConversationDTO>(`/api/conversations/${conversation.id}`, conversation).subscribe({
        next: updatedConversation => {
          console.log('Updated conversation received:', updatedConversation);
          // Reload conversations to ensure we have the latest data
          this.loadConversations();
          this.closeModal();
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error updating conversation:', error);
          this.isLoading.set(false);
        },
      });
    } else {
      // Create new conversation
      this.http.post<ConversationDTO>('/api/conversations/my', conversation).subscribe({
        next: createdConversation => {
          const currentConversations = this.conversations();
          this.conversations.set([createdConversation, ...currentConversations]);
          this.closeModal();
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error creating conversation:', error);
          this.isLoading.set(false);
        },
      });
    }
  }

  onDeleteConversation(): void {
    if (!this.currentConversation?.id) {
      return;
    }

    this.isLoading.set(true);
    this.http.delete(`/api/conversations/${this.currentConversation.id}`).subscribe({
      next: () => {
        const currentConversations = this.conversations();
        const updatedConversations = currentConversations.filter(c => c.id !== this.currentConversation?.id);
        this.conversations.set(updatedConversations);
        this.closeModal();
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Error deleting conversation:', error);
        this.isLoading.set(false);
      },
    });
  }

  closeModal(): void {
    this.showModal.set(false);
    this.isEditMode.set(false);
    this.currentConversation = null;
  }

  formatDate(date?: Dayjs | string): string {
    if (!date) return '-';
    const dayjsDate = typeof date === 'string' ? dayjs(date) : date;
    return dayjsDate.format('DD/MM/YYYY HH:mm');
  }

  getConversationSummary(conversation: ConversationDTO): string {
    const messageCount = conversation.messageList?.length || 0;
    const duration = this.getConversationDuration(conversation);
    return `${messageCount} mensajes - ${duration}`;
  }

  getConversationDuration(conversation: ConversationDTO): string {
    if (!conversation.start || !conversation.end) return '-';

    const start = typeof conversation.start === 'string' ? dayjs(conversation.start) : conversation.start;
    const end = typeof conversation.end === 'string' ? dayjs(conversation.end) : conversation.end;

    const diffMinutes = end.diff(start, 'minute');
    if (diffMinutes < 60) {
      return `${diffMinutes} minutos`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      return `${hours}h ${minutes}m`;
    }
  }

  getFeelingEmoji(feeling: Feeling): string {
    return FEELING_INFO[feeling]?.emoji || '😐';
  }

  getFeelingLabel(feeling: Feeling): string {
    return FEELING_INFO[feeling]?.label || feeling;
  }
}
