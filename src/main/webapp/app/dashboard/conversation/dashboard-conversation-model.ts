import { Dayjs } from 'dayjs/esm';
import { Feeling } from 'app/shared/enum/feeling';

export interface ConversationDTO {
  id?: string;
  name?: string;
  start?: Dayjs;
  end?: Dayjs;
  messageList?: MessageDTO[];
  context?: string;
  feelingList?: Feeling[];
  tagList?: TagDTO[];
  userId?: string;
}

export interface MessageDTO {
  id?: string;
  content?: string;
  time?: Dayjs;
  sender?: string;
  recipient?: string;
  feeling?: Feeling;
  tag?: string;
}

export interface TagDTO {
  id?: string;
  value: string;
  userId?: string;
}

export { Feeling };
