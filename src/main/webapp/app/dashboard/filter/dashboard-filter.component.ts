import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPlus, faMinus, faBroom, faSearch } from '@fortawesome/free-solid-svg-icons';
import dayjs from 'dayjs/esm';
import { DashboardFilterModel } from './dashboard-filter-model';
import { Feeling } from '../../shared/enum/feeling';
import { Tag } from '../../entities/tag/tab-model';
import { DashboardComponent } from '../dashboard.component';

@Component({
  selector: 'jhi-dashboard-filter',
  templateUrl: './dashboard-filter.component.html',
  styleUrls: ['./dashboard-filter.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
})
export class DashboardFilterComponent {
  faPlus = faPlus;
  faMinus = faMinus;
  faBroom = faBroom;
  faSearch = faSearch;

  @Input() tags: string[] = [];
  @Output() filterChange = new EventEmitter<DashboardFilterModel>();
  @Output() clearFilters = new EventEmitter<void>();
  @Output() search = new EventEmitter<DashboardFilterModel>();

  isExpanded = signal(false);

  // Filter properties
  filterText = '';
  filterStartDate = '';
  filterEndDate = '';
  filterTag = '';
  filterFeeling = '';

  // Available feelings for dropdown
  feelings = [
    { value: Feeling.HAPPY, label: 'Feliz' },
    { value: Feeling.LOVE, label: 'Amor' },
    { value: Feeling.ANGRY, label: 'Enojado' },
    { value: Feeling.SAD, label: 'Triste' },
    { value: Feeling.EXCITED, label: 'Emocionado' },
    { value: Feeling.GRATEFUL, label: 'Agradecido' },
    { value: Feeling.FRUSTRATED, label: 'Frustrado' },
    { value: Feeling.NEUTRAL, label: 'Neutral' },
  ];

  toggleExpanded(): void {
    this.isExpanded.set(!this.isExpanded());
  }

  onFilterChange(): void {
    const filter: DashboardFilterModel = {
      content: this.filterText || undefined,
      init: this.filterStartDate ? dayjs(this.filterStartDate) : null,
      end: this.filterEndDate ? dayjs(this.filterEndDate) : null,
      feelings: this.filterFeeling ? [this.filterFeeling as Feeling] : undefined,
      tag: this.filterTag ? [{ id: '', value: this.filterTag } as Tag] : undefined,
    };
    this.filterChange.emit(filter);
  }

  onSearch(): void {
    const filter: DashboardFilterModel = {
      content: this.filterText || undefined,
      init: this.filterStartDate ? dayjs(this.filterStartDate) : null,
      end: this.filterEndDate ? dayjs(this.filterEndDate) : null,
      feelings: this.filterFeeling ? [this.filterFeeling as Feeling] : undefined,
      tag: this.filterTag ? [{ id: '', value: this.filterTag } as Tag] : undefined,
    };
    this.search.emit(filter);
  }

  onClearFilters(): void {
    this.filterText = '';
    this.filterStartDate = '';
    this.filterEndDate = '';
    this.filterTag = '';
    this.filterFeeling = '';
    this.clearFilters.emit();
  }
}
