# Gemini - Análisis del Proyecto

Este documento sirve como un registro de nuestro trabajo en el proyecto "Forconversations". Aquí documentaremos la arquitectura, las decisiones de diseño y el progreso de las mejoras y evolutivos.

## Arquitectura del Proyecto

### Backend

- **Lenguaje:** Java 21
- **Framework:** Spring Boot 3.5.0
- **Gestor de dependencias:** <PERSON>ven
- **Generador de código:** JHipster 8.11.0
- **Base de datos:** MongoDB
- **Motor de búsqueda:** Elasticsearch
- **Seguridad:** Spring Security con OAuth 2.0
- **Otros:** jMolecules (para Domain-Driven Design), Google Cloud Speech.

### Frontend

- **Framework:** Angular 19
- **Entorno de ejecución:** Node.js 22.15.0
- **Gestor de paquetes:** npm 11.3.0
- **UI Framework:** Bootstrap y ng-bootstrap
- **Iconos:** Font Awesome
- **Testing:** Jest

### General

- El proyecto es una aplicación monolítica generada con J<PERSON>ip<PERSON>, con un frontend de Angular y un backend de Spring Boot.
- Utiliza Docker para la contenerización de la aplicación y sus servicios, lo que facilita el desarrollo y despliegue.
